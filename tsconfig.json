{"compilerOptions": {"target": "ES2020", "lib": ["ES2020", "DOM", "DOM.Iterable"], "module": "ESNext", "skipLibCheck": true, "moduleResolution": "bundler", "allowImportingTsExtensions": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "allowJs": true, "checkJs": false, "strict": true, "noUnusedLocals": false, "noUnusedParameters": false, "noFallthroughCasesInSwitch": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@components/*": ["./src/components/*"], "@hooks/*": ["./src/hooks/*"], "@assets/*": ["./src/assets/*"], "@theme": ["./src/theme.ts"], "@styles": ["./src/index.css"], "@utils/*": ["./src/utils/*"], "@constants/*": ["./src/constants/*"], "@types/*": ["./src/types/*"]}}, "include": ["src/**/*", "vite.config.ts"], "exclude": ["node_modules", "dist", "build"]}
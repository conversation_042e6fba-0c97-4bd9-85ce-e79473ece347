import React from 'react'
import { <PERSON>, Typo<PERSON>, <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>, <PERSON> } from '@mui/material'
import { Code as CodeIcon } from '@mui/icons-material'

// Example of using different aliases
import { useThemeMode } from '@hooks/useThemeMode'
// import SomeComponent from '@components/SomeComponent'  // Example
// import { CONSTANTS } from '@constants/app'             // Example
// import { utilityFunction } from '@utils/helpers'       // Example

const AliasDemo = (): React.JSX.Element => {
  const { mode } = useThemeMode()

  const aliases = [
    {
      alias: '@',
      path: './src',
      description: 'Root source directory',
      example: "import App from '@/App.jsx'",
    },
    {
      alias: '@components',
      path: './src/components',
      description: 'React components',
      example: "import Navigation from '@components/Navigation'",
    },
    {
      alias: '@hooks',
      path: './src/hooks',
      description: 'Custom React hooks',
      example: "import { useThemeMode } from '@hooks/useThemeMode'",
    },
    {
      alias: '@assets',
      path: './src/assets',
      description: 'Static assets (images, icons, etc.)',
      example: "import logo from '@assets/logo.svg'",
    },
    {
      alias: '@theme',
      path: './src/theme.js',
      description: 'MUI theme configuration',
      example: "import theme from '@theme'",
    },
    {
      alias: '@styles',
      path: './src/index.css',
      description: 'Global CSS styles',
      example: "import '@styles'",
    },
    {
      alias: '@utils',
      path: './src/utils',
      description: 'Utility functions',
      example: "import { formatDate } from '@utils/dateHelpers'",
    },
    {
      alias: '@constants',
      path: './src/constants',
      description: 'Application constants',
      example: "import { API_ENDPOINTS } from '@constants/api'",
    },
    {
      alias: '@types',
      path: './src/types',
      description: 'TypeScript type definitions',
      example: "import type { User } from '@types/user'",
    },
  ]

  return (
    <Box sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        <CodeIcon sx={{ mr: 2, fontSize: 32 }} />
        <Typography variant='h3' gutterBottom>
          Import Aliases Configuration
        </Typography>
      </Box>

      <Alert severity='info' sx={{ mb: 3 }}>
        <Typography variant='body1'>
          This project is configured with import aliases to make imports cleaner
          and more maintainable. Current mode:{' '}
          <Chip label={mode} size='small' color='primary' sx={{ ml: 1 }} />
        </Typography>
      </Alert>

      <Typography variant='h5' gutterBottom sx={{ mt: 4, mb: 2 }}>
        Available Aliases
      </Typography>

      <Box sx={{ display: 'grid', gap: 2 }}>
        {aliases.map(item => (
          <Card key={item.alias} variant='outlined'>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <Chip
                  label={item.alias}
                  color='primary'
                  size='small'
                  sx={{ mr: 2, fontFamily: 'monospace' }}
                />
                <Typography variant='body2' color='text.secondary'>
                  → {item.path}
                </Typography>
              </Box>

              <Typography variant='body1' sx={{ mb: 1 }}>
                {item.description}
              </Typography>

              <Box
                sx={{
                  backgroundColor: mode === 'dark' ? 'grey.900' : 'grey.100',
                  p: 1.5,
                  borderRadius: 1,
                  fontFamily: 'monospace',
                  fontSize: '0.875rem',
                  overflow: 'auto',
                }}
              >
                <Typography
                  variant='body2'
                  component='code'
                  sx={{
                    color: mode === 'dark' ? 'success.light' : 'success.dark',
                    fontFamily: 'inherit',
                  }}
                >
                  {item.example}
                </Typography>
              </Box>
            </CardContent>
          </Card>
        ))}
      </Box>

      <Card sx={{ mt: 4 }} variant='outlined'>
        <CardContent>
          <Typography variant='h6' gutterBottom>
            Configuration Files
          </Typography>
          <Typography variant='body2' sx={{ mb: 2 }}>
            The aliases are configured in the following files:
          </Typography>

          <Box sx={{ display: 'grid', gap: 1 }}>
            <Box
              sx={{
                backgroundColor: mode === 'dark' ? 'grey.900' : 'grey.100',
                p: 1,
                borderRadius: 1,
                fontFamily: 'monospace',
                fontSize: '0.875rem',
              }}
            >
              <Typography variant='body2' component='code'>
                📁 vite.config.js - Vite bundler configuration
              </Typography>
            </Box>
            <Box
              sx={{
                backgroundColor: mode === 'dark' ? 'grey.900' : 'grey.100',
                p: 1,
                borderRadius: 1,
                fontFamily: 'monospace',
                fontSize: '0.875rem',
              }}
            >
              <Typography variant='body2' component='code'>
                📁 jsconfig.json - IDE IntelliSense support
              </Typography>
            </Box>
          </Box>
        </CardContent>
      </Card>

      <Alert severity='success' sx={{ mt: 3 }}>
        <Typography variant='body2'>
          <strong>Benefits:</strong> Cleaner imports, better maintainability,
          easier refactoring, and improved IDE support with autocomplete and
          navigation.
        </Typography>
      </Alert>
    </Box>
  )
}

export default AliasDemo

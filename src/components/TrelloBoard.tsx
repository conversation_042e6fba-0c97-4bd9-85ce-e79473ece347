import { useState } from 'react'
import {
  <PERSON>,
  Card,
  Card<PERSON>ontent,
  <PERSON><PERSON><PERSON>,
  <PERSON>ton,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Chip,
  IconButton,
} from '@mui/material'
import {
  Add as AddIcon,
  MoreVert as MoreVertIcon,
  Person as PersonIcon,
} from '@mui/icons-material'

interface TrelloCard {
  id: number
  title: string
  description: string
  labels: string[]
}

interface TrelloList {
  id: number
  title: string
  cards: TrelloCard[]
}

type LabelColor =
  | 'primary'
  | 'secondary'
  | 'error'
  | 'warning'
  | 'success'
  | 'default'

const TrelloBoard = (): JSX.Element => {
  const [lists, setLists] = useState<TrelloList[]>([
    {
      id: 1,
      title: 'To Do',
      cards: [
        {
          id: 1,
          title: 'Design homepage',
          description: 'Create wireframes and mockups',
          labels: ['Design', 'High Priority'],
        },
        {
          id: 2,
          title: 'Setup database',
          description: 'Configure PostgreSQL',
          labels: ['Backend'],
        },
      ],
    },
    {
      id: 2,
      title: 'In Progress',
      cards: [
        {
          id: 3,
          title: 'Implement authentication',
          description: 'Add login/signup functionality',
          labels: ['Backend', 'Security'],
        },
      ],
    },
    {
      id: 3,
      title: 'Done',
      cards: [
        {
          id: 4,
          title: 'Project setup',
          description: 'Initialize React app with Vite',
          labels: ['Setup'],
        },
      ],
    },
  ])

  const [openDialog, setOpenDialog] = useState<boolean>(false)
  const [newCardTitle, setNewCardTitle] = useState<string>('')
  const [selectedListId, setSelectedListId] = useState<number | null>(null)

  const handleAddCard = (listId: number): void => {
    setSelectedListId(listId)
    setOpenDialog(true)
  }

  const handleCreateCard = (): void => {
    if (newCardTitle.trim()) {
      const newCard: TrelloCard = {
        id: Date.now(),
        title: newCardTitle,
        description: '',
        labels: [],
      }

      setLists(
        lists.map((list: TrelloList) =>
          list.id === selectedListId
            ? { ...list, cards: [...list.cards, newCard] }
            : list
        )
      )

      setNewCardTitle('')
      setOpenDialog(false)
    }
  }

  const getLabelColor = (label: string): LabelColor => {
    const colors: Record<string, LabelColor> = {
      Design: 'primary',
      Backend: 'secondary',
      'High Priority': 'error',
      Security: 'warning',
      Setup: 'success',
    }
    return colors[label] || 'default'
  }

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant='h4' gutterBottom>
        Project Board
      </Typography>

      <Box sx={{ display: 'flex', gap: 3, overflowX: 'auto', pb: 2 }}>
        {lists.map(list => (
          <Box
            key={list.id}
            sx={{
              minWidth: 300,
              maxWidth: 300,
              backgroundColor: '#f4f5f7',
              borderRadius: 2,
              p: 2,
            }}
          >
            <Box
              sx={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                mb: 2,
              }}
            >
              <Typography variant='h6' fontWeight='bold'>
                {list.title}
              </Typography>
              <IconButton size='small'>
                <MoreVertIcon />
              </IconButton>
            </Box>

            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
              {list.cards.map(card => (
                <Card
                  key={card.id}
                  sx={{ cursor: 'pointer', '&:hover': { boxShadow: 3 } }}
                >
                  <CardContent sx={{ p: 2, '&:last-child': { pb: 2 } }}>
                    <Typography
                      variant='subtitle1'
                      fontWeight='medium'
                      gutterBottom
                    >
                      {card.title}
                    </Typography>
                    {card.description && (
                      <Typography
                        variant='body2'
                        color='text.secondary'
                        gutterBottom
                      >
                        {card.description}
                      </Typography>
                    )}
                    {card.labels.length > 0 && (
                      <Box
                        sx={{
                          display: 'flex',
                          flexWrap: 'wrap',
                          gap: 0.5,
                          mt: 1,
                        }}
                      >
                        {card.labels.map((label, index) => (
                          <Chip
                            key={index}
                            label={label}
                            size='small'
                            color={getLabelColor(label)}
                            variant='outlined'
                          />
                        ))}
                      </Box>
                    )}
                    <Box
                      sx={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                        mt: 2,
                      }}
                    >
                      <Box sx={{ display: 'flex', gap: 0.5 }}>
                        <IconButton size='small'>
                          <PersonIcon fontSize='small' />
                        </IconButton>
                      </Box>
                    </Box>
                  </CardContent>
                </Card>
              ))}

              <Button
                variant='outlined'
                startIcon={<AddIcon />}
                onClick={() => handleAddCard(list.id)}
                sx={{
                  borderStyle: 'dashed',
                  color: 'text.secondary',
                  borderColor: 'divider',
                  '&:hover': {
                    borderStyle: 'solid',
                    borderColor: 'primary.main',
                    color: 'primary.main',
                  },
                }}
              >
                Add a card
              </Button>
            </Box>
          </Box>
        ))}
      </Box>

      {/* Add Card Dialog */}
      <Dialog
        open={openDialog}
        onClose={() => setOpenDialog(false)}
        maxWidth='sm'
        fullWidth
      >
        <DialogTitle>Add New Card</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin='dense'
            label='Card Title'
            fullWidth
            variant='outlined'
            value={newCardTitle}
            onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
              setNewCardTitle(e.target.value)
            }
            onKeyDown={(e: React.KeyboardEvent<HTMLInputElement>) => {
              if (e.key === 'Enter') {
                handleCreateCard()
              }
            }}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenDialog(false)}>Cancel</Button>
          <Button onClick={handleCreateCard} variant='contained'>
            Add Card
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  )
}

export default TrelloBoard

import {
  Box,
  Typography,
  Card,
  Card<PERSON>ontent,
  Grid,
  Divider,
  Chip,
} from '@mui/material'

const TypographyShowcase = () => {
  const fontWeights = [
    { name: 'Light', weight: 300, description: 'For subtle text' },
    { name: 'Regular', weight: 400, description: 'Default body text' },
    { name: 'Medium', weight: 500, description: 'Emphasis and headings' },
    { name: 'Bold', weight: 700, description: 'Strong emphasis' },
  ]

  const typographyVariants = [
    { variant: 'h1', label: 'Heading 1', sample: 'The quick brown fox jumps' },
    { variant: 'h2', label: 'Heading 2', sample: 'The quick brown fox jumps' },
    { variant: 'h3', label: 'Heading 3', sample: 'The quick brown fox jumps' },
    { variant: 'h4', label: 'Heading 4', sample: 'The quick brown fox jumps' },
    { variant: 'h5', label: 'Heading 5', sample: 'The quick brown fox jumps' },
    { variant: 'h6', label: 'Heading 6', sample: 'The quick brown fox jumps' },
    {
      variant: 'subtitle1',
      label: 'Subtitle 1',
      sample: 'The quick brown fox jumps over the lazy dog',
    },
    {
      variant: 'subtitle2',
      label: 'Subtitle 2',
      sample: 'The quick brown fox jumps over the lazy dog',
    },
    {
      variant: 'body1',
      label: 'Body 1',
      sample:
        'The quick brown fox jumps over the lazy dog. This is the default body text style.',
    },
    {
      variant: 'body2',
      label: 'Body 2',
      sample:
        'The quick brown fox jumps over the lazy dog. This is secondary body text.',
    },
    {
      variant: 'caption',
      label: 'Caption',
      sample: 'The quick brown fox jumps over the lazy dog',
    },
    {
      variant: 'overline',
      label: 'Overline',
      sample: 'The quick brown fox jumps',
    },
  ]

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant='h3' gutterBottom>
        Roboto Font Typography Showcase
      </Typography>

      <Typography variant='body1' paragraph>
        This application now uses the Roboto font family as the primary
        typeface. Roboto is Google's signature family of fonts, designed for
        optimal readability across platforms and devices.
      </Typography>

      {/* Font Weights Section */}
      <Box sx={{ mb: 4 }}>
        <Typography variant='h4' gutterBottom>
          Font Weights
        </Typography>
        <Grid container spacing={2}>
          {fontWeights.map(weight => (
            <Grid item xs={12} sm={6} md={3} key={weight.weight}>
              <Card>
                <CardContent>
                  <Typography
                    variant='h6'
                    sx={{ fontWeight: weight.weight }}
                    gutterBottom
                  >
                    Roboto {weight.name}
                  </Typography>
                  <Chip
                    label={`Weight: ${weight.weight}`}
                    size='small'
                    color='primary'
                    sx={{ mb: 1 }}
                  />
                  <Typography variant='body2' color='text.secondary'>
                    {weight.description}
                  </Typography>
                  <Typography
                    variant='body1'
                    sx={{ fontWeight: weight.weight, mt: 1 }}
                  >
                    The quick brown fox jumps over the lazy dog.
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      </Box>

      <Divider sx={{ my: 4 }} />

      {/* Typography Variants Section */}
      <Box>
        <Typography variant='h4' gutterBottom>
          Typography Variants
        </Typography>
        <Typography variant='body1' paragraph>
          All typography variants are now using the Roboto font with optimized
          font weights and line heights for better readability.
        </Typography>

        <Grid container spacing={3}>
          {typographyVariants.map(item => (
            <Grid item xs={12} key={item.variant}>
              <Card sx={{ p: 2 }}>
                <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 2 }}>
                  <Box sx={{ minWidth: 120 }}>
                    <Chip
                      label={item.label}
                      variant='outlined'
                      size='small'
                      color='secondary'
                    />
                  </Box>
                  <Box sx={{ flex: 1 }}>
                    <Typography variant={item.variant}>
                      {item.sample}
                    </Typography>
                  </Box>
                </Box>
              </Card>
            </Grid>
          ))}
        </Grid>
      </Box>

      {/* Font Loading Information */}
      <Box sx={{ mt: 4, p: 3, bgcolor: 'background.paper', borderRadius: 2 }}>
        <Typography variant='h5' gutterBottom>
          Font Loading Strategy
        </Typography>
        <Typography variant='body1' paragraph>
          This application uses a dual font loading strategy for optimal
          performance:
        </Typography>
        <Box component='ul' sx={{ pl: 3 }}>
          <Typography component='li' variant='body1' paragraph>
            <strong>Google Fonts CDN:</strong> Fast loading via CDN with
            font-display: swap for immediate text rendering
          </Typography>
          <Typography component='li' variant='body1' paragraph>
            <strong>Self-hosted fonts:</strong> @fontsource/roboto package for
            offline support and better caching control
          </Typography>
        </Box>
        <Typography variant='body2' color='text.secondary'>
          The browser will automatically choose the best loading method based on
          availability.
        </Typography>
      </Box>
    </Box>
  )
}

export default TypographyShowcase

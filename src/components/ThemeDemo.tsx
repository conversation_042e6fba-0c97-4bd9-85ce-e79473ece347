import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  Button,
  Chip,
  Paper,
  Switch,
  FormControlLabel,
  Alert,
  Divider,
} from '@mui/material'
import {
  LightMode as LightModeIcon,
  DarkMode as DarkModeIcon,
  Palette as PaletteIcon,
  Computer as ComputerIcon,
} from '@mui/icons-material'
import { useThemeMode } from '@hooks/useThemeMode'

const ThemeDemo = (): JSX.Element => {
  const {
    mode,
    resolvedMode,
    systemMode,
    toggleMode,
    setLight,
    setDark,
    setSystemMode,
    colors,
    cssVars,
    theme: muiTheme,
  } = useThemeMode()

  const colorPalette = [
    { name: 'Primary', color: colors.primary },
    { name: 'Secondary', color: colors.secondary },
    { name: 'Success', color: muiTheme.palette.success.main },
    { name: 'Warning', color: muiTheme.palette.warning.main },
    { name: 'Error', color: muiTheme.palette.error.main },
    { name: 'Info', color: muiTheme.palette.info.main },
  ]

  const backgroundColors = [
    { name: 'Default Background', color: colors.background },
    { name: 'Paper Surface', color: colors.paper },
    { name: 'Primary Text', color: colors.text },
    { name: 'Secondary Text', color: colors.textSecondary },
  ]

  return (
    <Box sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        <PaletteIcon sx={{ mr: 2, fontSize: 32 }} />
        <Typography variant='h3' gutterBottom>
          Dark & Light Mode Demo
        </Typography>
      </Box>

      <Typography variant='body1' sx={{ mb: 2 }}>
        This application supports both light and dark themes with automatic
        system preference detection and manual toggle functionality.
      </Typography>

      {/* Theme Toggle Section */}
      <Card sx={{ mb: 4 }}>
        <CardContent>
          <Typography variant='h5' gutterBottom>
            Theme Controls
          </Typography>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
            <FormControlLabel
              control={
                <Switch
                  checked={mode === 'dark'}
                  onChange={toggleMode}
                  icon={<LightModeIcon />}
                  checkedIcon={<DarkModeIcon />}
                />
              }
              label={`Current mode: ${mode}`}
            />
            <Button
              variant='outlined'
              onClick={toggleMode}
              startIcon={
                mode === 'light' ? <DarkModeIcon /> : <LightModeIcon />
              }
            >
              Switch to {mode === 'light' ? 'Dark' : 'Light'} Mode
            </Button>
          </Box>

          <Divider sx={{ my: 2 }} />

          <Box sx={{ display: 'flex', gap: 1, mb: 2 }}>
            <Button
              variant={mode === 'light' ? 'contained' : 'outlined'}
              onClick={setLight}
              startIcon={<LightModeIcon />}
              size='small'
            >
              Light
            </Button>
            <Button
              variant={mode === 'dark' ? 'contained' : 'outlined'}
              onClick={setDark}
              startIcon={<DarkModeIcon />}
              size='small'
            >
              Dark
            </Button>
            <Button
              variant={mode === 'system' ? 'contained' : 'outlined'}
              onClick={setSystemMode}
              startIcon={<ComputerIcon />}
              size='small'
            >
              System ({systemMode})
            </Button>
          </Box>

          {mode === 'system' && (
            <Alert severity='info' sx={{ mb: 2 }}>
              Using system preference: <strong>{systemMode}</strong> mode
              {resolvedMode !== systemMode && ` (resolved to ${resolvedMode})`}
            </Alert>
          )}

          {cssVars && (
            <Alert severity='success' sx={{ mb: 2 }}>
              CSS Variables enabled for optimal performance
            </Alert>
          )}
          <Alert severity='info'>
            Theme preference is automatically managed by MUI's useColorScheme
            hook with system preference detection and localStorage persistence.
          </Alert>
        </CardContent>
      </Card>

      {/* Color Palette */}
      <Card sx={{ mb: 4 }}>
        <CardContent>
          <Typography variant='h5' gutterBottom>
            Color Palette
          </Typography>
          <Grid container spacing={2}>
            {colorPalette.map(item => (
              <Grid item xs={6} sm={4} md={2} key={item.name}>
                <Paper
                  sx={{
                    p: 2,
                    textAlign: 'center',
                    backgroundColor: item.color,
                    color: muiTheme.palette.getContrastText(item.color),
                  }}
                >
                  <Typography variant='subtitle2'>{item.name}</Typography>
                  <Typography
                    variant='caption'
                    sx={{ fontFamily: 'monospace' }}
                  >
                    {item.color}
                  </Typography>
                </Paper>
              </Grid>
            ))}
          </Grid>
        </CardContent>
      </Card>

      {/* Component Examples */}
      <Card sx={{ mb: 4 }}>
        <CardContent>
          <Typography variant='h5' gutterBottom>
            Component Examples
          </Typography>
          <Grid container spacing={3}>
            {/* Buttons */}
            <Grid item xs={12} md={6}>
              <Typography variant='h6' gutterBottom>
                Buttons
              </Typography>
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
                <Button variant='contained' color='primary'>
                  Primary
                </Button>
                <Button variant='contained' color='secondary'>
                  Secondary
                </Button>
                <Button variant='outlined' color='primary'>
                  Outlined
                </Button>
                <Button variant='text' color='primary'>
                  Text
                </Button>
              </Box>
            </Grid>

            {/* Chips */}
            <Grid item xs={12} md={6}>
              <Typography variant='h6' gutterBottom>
                Chips
              </Typography>
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
                <Chip label='Default' />
                <Chip label='Primary' color='primary' />
                <Chip label='Secondary' color='secondary' />
                <Chip label='Success' color='success' />
                <Chip label='Warning' color='warning' />
                <Chip label='Error' color='error' />
              </Box>
            </Grid>

            {/* Alerts */}
            <Grid item xs={12}>
              <Typography variant='h6' gutterBottom>
                Alerts
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <Alert severity='success'>
                    This is a success alert in {mode} mode!
                  </Alert>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Alert severity='warning'>
                    This is a warning alert in {mode} mode!
                  </Alert>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Alert severity='error'>
                    This is an error alert in {mode} mode!
                  </Alert>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Alert severity='info'>
                    This is an info alert in {mode} mode!
                  </Alert>
                </Grid>
              </Grid>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Background Examples */}
      <Card sx={{ mb: 4 }}>
        <CardContent>
          <Typography variant='h5' gutterBottom>
            Background & Surface Examples
          </Typography>
          <Grid container spacing={2} sx={{ mb: 3 }}>
            {backgroundColors.map(item => (
              <Grid item xs={12} sm={6} md={3} key={item.name}>
                <Paper
                  sx={{
                    p: 2,
                    backgroundColor: item.color,
                    color: muiTheme.palette.getContrastText(item.color),
                    border: 1,
                    borderColor: 'divider',
                  }}
                >
                  <Typography variant='subtitle2'>{item.name}</Typography>
                  <Typography
                    variant='caption'
                    sx={{ fontFamily: 'monospace' }}
                  >
                    {item.color}
                  </Typography>
                </Paper>
              </Grid>
            ))}
          </Grid>

          <Typography variant='h6' gutterBottom>
            Theme Surfaces
          </Typography>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6} md={3}>
              <Paper
                sx={{
                  p: 2,
                  backgroundColor: 'background.default',
                  border: 1,
                  borderColor: 'divider',
                }}
              >
                <Typography variant='subtitle2'>Default Background</Typography>
                <Typography variant='body2' color='text.secondary'>
                  {colors.background}
                </Typography>
              </Paper>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Paper sx={{ p: 2 }}>
                <Typography variant='subtitle2'>Paper Surface</Typography>
                <Typography variant='body2' color='text.secondary'>
                  {colors.paper}
                </Typography>
              </Paper>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Paper
                sx={{
                  p: 2,
                  backgroundColor: 'primary.main',
                  color: 'primary.contrastText',
                }}
              >
                <Typography variant='subtitle2'>Primary Surface</Typography>
                <Typography variant='body2' sx={{ opacity: 0.8 }}>
                  {colors.primary}
                </Typography>
              </Paper>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Paper
                sx={{
                  p: 2,
                  backgroundColor: 'secondary.main',
                  color: 'secondary.contrastText',
                }}
              >
                <Typography variant='subtitle2'>Secondary Surface</Typography>
                <Typography variant='body2' sx={{ opacity: 0.8 }}>
                  {colors.secondary}
                </Typography>
              </Paper>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* CSS Variables Information */}
      {cssVars && (
        <Card>
          <CardContent>
            <Typography variant='h5' gutterBottom>
              CSS Variables (Advanced)
            </Typography>
            <Typography variant='body2' sx={{ mb: 2 }}>
              This theme uses MUI's CSS variables for optimal performance. Here
              are some examples:
            </Typography>
            <Paper
              sx={{
                p: 2,
                backgroundColor: 'grey.100',
                fontFamily: 'monospace',
              }}
            >
              <Typography variant='body2' component='div'>
                --mui-palette-primary-main:{' '}
                {cssVars.palette?.primary?.main || 'N/A'}
              </Typography>
              <Typography variant='body2' component='div'>
                --mui-palette-background-default:{' '}
                {cssVars.palette?.background?.default || 'N/A'}
              </Typography>
              <Typography variant='body2' component='div'>
                --mui-palette-text-primary:{' '}
                {cssVars.palette?.text?.primary || 'N/A'}
              </Typography>
            </Paper>
          </CardContent>
        </Card>
      )}
    </Box>
  )
}

export default ThemeDemo

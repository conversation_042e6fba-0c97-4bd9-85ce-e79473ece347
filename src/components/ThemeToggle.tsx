import React from 'react'
import { IconButton, Tooltip } from '@mui/material'
import {
  LightMode as LightModeIcon,
  DarkMode as DarkModeIcon,
} from '@mui/icons-material'
import { useThemeMode } from '@hooks/useThemeMode'

const ThemeToggle = (): React.JSX.Element => {
  const { mode, toggleMode, theme } = useThemeMode()

  return (
    <Tooltip title={`Switch to ${mode === 'light' ? 'dark' : 'light'} mode`}>
      <IconButton
        onClick={toggleMode}
        color='inherit'
        sx={{
          transition: 'transform 0.3s ease-in-out',
          '&:hover': {
            transform: 'rotate(180deg)',
          },
        }}
      >
        {mode === 'light' ? (
          <DarkModeIcon />
        ) : (
          <LightModeIcon sx={{ color: theme.palette.warning.main }} />
        )}
      </IconButton>
    </Tooltip>
  )
}

export default ThemeToggle

import { useColorScheme, useTheme } from '@mui/material'

/**
 * Custom hook that provides enhanced theme utilities
 * Built on top of MUI's useColorScheme for additional functionality
 */
export const useThemeMode = () => {
  const { mode, setMode, systemMode } = useColorScheme()
  const theme = useTheme()

  // Toggle between light and dark modes
  const toggleMode = () => {
    setMode(mode === 'light' ? 'dark' : 'light')
  }

  // Set to system preference
  const setSystemMode = () => {
    setMode('system')
  }

  // Check if current mode is dark
  const isDark = mode === 'dark'

  // Check if current mode is light
  const isLight = mode === 'light'

  // Check if using system preference
  const isSystem = mode === 'system'

  // Get the actual resolved mode (useful when mode is 'system')
  const resolvedMode = mode === 'system' ? systemMode : mode

  // Get theme-aware colors
  const colors = {
    primary: theme.palette.primary.main,
    secondary: theme.palette.secondary.main,
    background: theme.palette.background.default,
    paper: theme.palette.background.paper,
    text: theme.palette.text.primary,
    textSecondary: theme.palette.text.secondary,
  }

  // Get CSS variables if available
  const cssVars = theme.vars || null

  return {
    // Mode state
    mode,
    resolvedMode,
    systemMode,
    isDark,
    isLight,
    isSystem,
    
    // Mode setters
    setMode,
    toggleMode,
    setSystemMode,
    
    // Theme utilities
    theme,
    colors,
    cssVars,
    
    // Convenience methods
    setLight: () => setMode('light'),
    setDark: () => setMode('dark'),
  }
}

export default useThemeMode

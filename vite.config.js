import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import { fileURLToPath, URL } from 'node:url'

// https://vite.dev/config/
export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      // Root alias
      '@': fileURLToPath(new URL('./src', import.meta.url)),

      // Component aliases
      '@components': fileURLToPath(
        new URL('./src/components', import.meta.url)
      ),

      '@hooks': fileURLToPath(new URL('./src/hooks', import.meta.url)),
      '@assets': fileURLToPath(new URL('./src/assets', import.meta.url)),

      // Specific aliases for commonly used files
      '@theme': fileURLToPath(new URL('./src/theme.js', import.meta.url)),
      '@styles': fileURLToPath(new URL('./src/index.css', import.meta.url)),

      // Utility aliases (for future expansion)
      '@utils': fileURLToPath(new URL('./src/utils', import.meta.url)),
      '@constants': fileURLToPath(new URL('./src/constants', import.meta.url)),
      '@types': fileURLToPath(new URL('./src/types', import.meta.url)),
    },
  },
})

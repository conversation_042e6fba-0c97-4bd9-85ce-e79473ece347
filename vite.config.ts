import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'

// https://vite.dev/config/
export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      // Root alias
      '@': path.resolve(__dirname, './src'),

      // Component aliases
      '@components': path.resolve(__dirname, './src/components'),

      '@hooks': path.resolve(__dirname, './src/hooks'),
      '@assets': path.resolve(__dirname, './src/assets'),

      // Specific aliases for commonly used files
      '@theme': path.resolve(__dirname, './src/theme.ts'),
      '@styles': path.resolve(__dirname, './src/index.css'),

      // Utility aliases (for future expansion)
      '@utils': path.resolve(__dirname, './src/utils'),
      '@constants': path.resolve(__dirname, './src/constants'),
      '@types': path.resolve(__dirname, './src/types'),
    },
  },
})
